# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
*.csv

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
.vscode
.idea
# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# local
bun.lock
pnpm-lock.yaml

# typescript
*.tsbuildinfo
next-env.d.ts
