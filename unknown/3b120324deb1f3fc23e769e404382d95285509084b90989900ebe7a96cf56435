"use client";

import Link from "next/link";
import { Button } from "./ui/button";
import { usePathname } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Menu, Globe, ChevronDown, Search, X } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { signIn, signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { ThemeToggle } from "@/components/ThemeToggle";
import { gamesData } from "@/data/games";
import { gameDetailsData } from "@/data/game-details";


interface HeaderProps {
  header: {
    logo: string;
    nav: {
      features: string;
      pricing: string;
      examples: string;
      docs: string;
    };
    cta: {
      login: string;
      signup: string;
    };
    userMenu: {
      myOrders: string;
      signOut: string;
    };
  };
}

export default function Header({ header }: HeaderProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Array<{
    id: string;
    title: string;
    category: string;
    image: string;
  }>>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLDivElement>(null);
  const t = useTranslations('Navigation');
  const tHome = useTranslations('Home');

  // 监听页面滚动，添加背景效果
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理搜索
  useEffect(() => {
    if (searchQuery.trim()) {
      const results: Array<{
        id: string;
        title: string;
        category: string;
        image: string;
      }> = [];

      // 遍历所有游戏数据
      Object.entries(gamesData).forEach(([category, games]) => {
        games.forEach(game => {
          const gameDetail = gameDetailsData[game.id];
          if (gameDetail) {
            const title = gameDetail.title[currentLocale as keyof typeof gameDetail.title];
            const categoryName = gameDetail.category[currentLocale as keyof typeof gameDetail.category];
            
            // 根据当前语言搜索标题和分类
            if (
              title.toLowerCase().includes(searchQuery.toLowerCase()) ||
              categoryName.toLowerCase().includes(searchQuery.toLowerCase())
            ) {
              results.push({
                id: game.id,
                title,
                category: categoryName,
                image: game.image
              });
            }
          }
        });
      });

      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, currentLocale]);

  // 点击外部关闭搜索
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setSearchOpen(false);
      }
    };

    if (searchOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [searchOpen]);

  const switchLocale = (locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    window.location.href = newPathname;
  };

  // 游戏分类数据
  const gameCategories = [
    { id: 'action', name: t('categoryList.action'), icon: '🎮' },
    { id: 'adventure', name: t('categoryList.adventure'), icon: '🗺️' },
    { id: 'racing', name: t('categoryList.racing'), icon: '🏎️' },
    { id: 'shooting', name: t('categoryList.shooting'), icon: '🔫' },
    { id: 'horror', name: t('categoryList.horror'), icon: '👻' },
    { id: 'strategy', name: t('categoryList.strategy'), icon: '⚔️' },
    { id: 'sports', name: t('categoryList.sports'), icon: '⚽' },
    { id: 'simulation', name: t('categoryList.simulation'), icon: '🎯' },
    { id: 'puzzle', name: t('categoryList.puzzle'), icon: '🧩' },
    { id: 'sandbox', name: t('categoryList.sandbox'), icon: '🏗️' }
  ];

  return (
    <div className={`w-full px-4 sm:px-6 lg:px-8 transition-all duration-300 ${scrolled ? 'py-2' : 'py-4'}`}>
      <nav className="relative flex items-center justify-start gap-4 lg:gap-6 xl:gap-8">
        {/* Left: Logo */}
        <motion.div 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex-none"
        >
          <Link href={`/${currentLocale}`} className="flex items-center gap-2" aria-label={t('home')}>
            <Image
              src="/logo.jpg"
              alt="FreeHubGames Logo"
              width={40}
              height={40}
              className="rounded-full"
              priority
            />
            <div className="flex items-center text-xl font-bold" role="banner">
              <span>Free</span>
              <span>Hub</span>
              <span className="text-gradient">Games</span>
            </div>
          </Link>
        </motion.div>

        {/* Navigation Links - Desktop Only */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="hidden lg:flex items-center"
        >
          <div className="flex items-center space-x-1 lg:space-x-2 xl:space-x-3">
            {/* 游戏分类并排展示 - 显示所有10个分类 */}
            {gameCategories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
              >
                <Link
                  href={`/${currentLocale}/games/${category.id}`}
                  className="flex items-center gap-1 text-xs lg:text-sm font-medium text-muted-foreground hover:text-foreground transition-all duration-200 whitespace-nowrap px-1 lg:px-1.5 xl:px-2 py-1 rounded-md hover:bg-secondary/60 hover:scale-105"
                  aria-label={`${t('categoryList.view')} ${category.name} ${tHome('games')}`}
                >
                  <span className="text-sm lg:text-base">{category.icon}</span>
                  <span className="hidden lg:inline">{category.name}</span>
                  <span className="lg:hidden text-xs">
                    {currentLocale === 'zh'
                      ? category.name.replace('游戏', '')
                      : category.name.replace(' Games', '')
                    }
                  </span>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Medium screens: Simplified navigation */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="hidden md:flex lg:hidden items-center justify-center flex-1 px-4"
        >
          <div className="flex items-center space-x-4">
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <Link
                href={`/${currentLocale}`}
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              >
                {t('home')}
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="relative group"
            >
              <button
                className="flex items-center text-sm font-medium text-foreground hover:text-foreground/80 transition-colors px-2 py-1 rounded-md"
                aria-label={t('categories')}
                aria-expanded={isDropdownOpen}
                aria-haspopup="true"
              >
                {t('categories')} <ChevronDown className="ml-1 h-4 w-4 transition-transform duration-200 group-hover:rotate-180" aria-hidden="true" />
              </button>
              <div className="absolute left-1/2 -translate-x-1/2 mt-2 w-56 rounded-lg shadow-lg py-2 bg-card border border-border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                <div className="grid grid-cols-1 gap-1">
                  {gameCategories.map(category => (
                    <Link
                      key={category.id}
                      href={`/${currentLocale}/games/${category.id}`}
                      className="flex items-center gap-2 px-4 py-2 text-sm hover:bg-muted transition-colors w-full text-left text-foreground hover:text-foreground/80"
                      aria-label={`${t('categoryList.view')} ${category.name} ${tHome('games')}`}
                    >
                      <span className="text-base">{category.icon}</span>
                      <span>{category.name}</span>
                    </Link>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
        
        {/* Right: Language & CTA Buttons */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="hidden md:flex items-center space-x-4 ml-auto"
        >
          {/* 搜索按钮 */}
          <button
            onClick={() => setSearchOpen(!searchOpen)}
            className="text-foreground hover:text-foreground/80 transition-colors"
            aria-label={t('search')}
            aria-expanded={searchOpen}
          >
            <Search className="h-5 w-5" aria-hidden="true" />
          </button>
          
          

          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-foreground transition-colors px-3 py-1.5 rounded-full hover:bg-secondary">
              <Globe className="h-4 w-4" />
              <span>{localeNames[currentLocale]}</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {locales.map((locale) => (
                <DropdownMenuItem
                  key={locale}
                  onClick={() => switchLocale(locale)}
                  className="cursor-pointer"
                >
                  {localeNames[locale]}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <ThemeToggle />
          
          {/* <div className="flex items-center space-x-3">
            {session ? (
              <div className="relative" ref={dropdownRef}>
                <div>
                  <button
                    type="button"
                    className="flex items-center rounded-full bg-white ring-2 ring-primary/10 p-0.5 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all hover:ring-primary/30"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  >
                    <span className="sr-only">打开用户菜单</span>
                    {session.user?.image ? (
                      <Image
                        className="h-8 w-8 rounded-full"
                        src={session.user.image}
                        alt={session.user.name || ''}
                        width={32}
                        height={32}
                        unoptimized
                        priority
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-primary font-medium">
                          {session.user?.name?.charAt(0) || '?'}
                        </span>
                      </div>
                    )}
                  </button>
                </div>

                {isDropdownOpen && (
                  <motion.div 
                    initial={{ opacity: 0, y: 10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-lg bg-card py-1 shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden"
                  >
                    <div className="px-4 py-3 text-sm">
                      <div className="font-medium">{session.user?.name}</div>
                      <div className="text-muted-foreground text-xs mt-1 truncate">{session.user?.email}</div>
                    </div>
                    <div className="border-t border-border" />
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="block px-4 py-2 text-sm hover:bg-secondary transition-colors"
                    >
                      {header.userMenu.myOrders}
                    </Link>
                    <button
                      type="button"
                      onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}
                      className="block w-full px-4 py-2 text-left text-sm hover:bg-secondary transition-colors"
                    >
                      {header.userMenu.signOut}
                    </button>
                  </motion.div>
                )}
              </div>
            ) : (
              <div className="flex items-center">
                <Button 
                  onClick={() => signIn()} 
                  size="sm" 
                  className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-full px-6 button-hover"
                >
                  {header.cta.login}
                </Button>
              </div>
            )}
          </div> */}
        </motion.div>

        {/* Mobile Menu Button */}
        <div className="flex md:hidden">
          {/* 搜索按钮 */}
          <button
            onClick={() => setSearchOpen(!searchOpen)}
            className="text-foreground hover:text-foreground/80 transition-colors mr-4"
            aria-label={t('search')}
            aria-expanded={searchOpen}
          >
            <Search className="h-5 w-5" aria-hidden="true" />
          </button>
          
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9 hover:bg-secondary">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="border-l border-border">
              <SheetHeader>
                <SheetTitle className="flex items-center">
                  <span className="text-xl font-bold">Free</span>
                  <span className="text-xl font-bold">Hub</span>
                  <span className="text-xl font-bold text-gradient">Games</span>
                </SheetTitle>
              </SheetHeader>
              <div className="flex flex-col space-y-4 mt-6">
                <Link
                  href={`/${currentLocale}`}
                  className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('home')}
                </Link>
                
                <div className="block">
                  <details className="group">
                    <summary className="list-none flex justify-between items-center cursor-pointer text-sm text-muted-foreground hover:text-foreground transition-colors">
                      {t('categories')} <ChevronDown className="h-4 w-4 group-open:rotate-180 transition-transform" />
                    </summary>
                    <div className="mt-2 ml-4 grid gap-2">
                      {gameCategories.map(category => (
                        <Link
                          key={category.id}
                          href={`/${currentLocale}/games/${category.id}`}
                          className="flex items-center gap-2 py-2 px-3 text-sm text-muted-foreground hover:text-foreground transition-colors rounded-md hover:bg-secondary"
                        >
                          <span className="text-base">{category.icon}</span>
                          <span>{category.name}</span>
                        </Link>
                      ))}
                    </div>
                  </details>
                </div>
                
                {/* <Link
                  href={`/${currentLocale}/popular`}
                  className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('popular')}
                </Link>
                
                <Link
                  href={`/${currentLocale}/new`}
                  className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  {t('new')}
                </Link> */}
                
                <DropdownMenu>
                  <DropdownMenuTrigger className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-foreground transition-colors mt-2">
                    <Globe className="h-4 w-4" />
                    <span>{localeNames[currentLocale]}</span>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {locales.map((locale) => (
                      <DropdownMenuItem
                        key={locale}
                        onClick={() => switchLocale(locale)}
                        className="cursor-pointer"
                      >
                        {localeNames[locale]}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
                
                {session ? (
                  <div className="flex flex-col space-y-3 pt-4 border-t border-border">
                    <div className="flex items-center space-x-3">
                      {session.user?.image ? (
                        <Image
                          className="h-8 w-8 rounded-full"
                          src={session.user.image}
                          alt={session.user.name || ''}
                          width={32}
                          height={32}
                          unoptimized
                        />
                      ) : (
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <span className="text-primary font-medium">
                            {session.user?.name?.charAt(0) || '?'}
                          </span>
                        </div>
                      )}
                      <div>
                        <div className="font-medium text-sm">{session.user?.name}</div>
                        <div className="text-muted-foreground text-xs truncate">{session.user?.email}</div>
                      </div>
                    </div>
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {header.userMenu.myOrders}
                    </Link>
                    <button
                      type="button"
                      onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}
                      className="flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {header.userMenu.signOut}
                    </button>
                  </div>
                ) : (
                  <div className="flex flex-col space-y-3 pt-4 border-t border-border">
                    <Button 
                      onClick={() => signIn()} 
                      size="sm" 
                      className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-full button-hover"
                    >
                      {header.cta.login}
                    </Button>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </nav>
      
      {/* 搜索弹出层 */}
      {searchOpen && (
        <motion.div
          ref={searchRef}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className="absolute top-full left-0 right-0 z-50 bg-card shadow-lg border border-border rounded-lg p-4 mt-2 mx-4"
        >
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('search')}
              className="w-full px-4 py-2 rounded-full bg-background border border-input focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              autoFocus
            />
            <button
              onClick={() => {
                setSearchOpen(false);
                setSearchQuery('');
                setSearchResults([]);
              }}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* 搜索结果 */}
          {searchResults.length > 0 && (
            <div className="mt-4 max-h-[400px] overflow-y-auto">
              <div className="grid grid-cols-1 gap-2">
                {searchResults.map((result) => (
                  <Link
                    key={result.id}
                    href={`/${currentLocale}/game/${result.id}`}
                    className="flex items-center space-x-3 p-2 rounded-lg hover:bg-secondary transition-colors"
                    onClick={() => {
                      setSearchOpen(false);
                      setSearchQuery('');
                      setSearchResults([]);
                    }}
                  >
                    <Image
                      src={result.image}
                      alt={result.title}
                      width={40}
                      height={40}
                      className="rounded-lg object-cover"
                    />
                    <div>
                      <div className="font-medium">{result.title}</div>
                      <div className="text-sm text-muted-foreground">{result.category}</div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
}