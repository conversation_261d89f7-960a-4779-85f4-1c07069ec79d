/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://freehubgames.com',
  generateRobotsTxt: true,
  generateIndexSitemap: true,
  changefreq: 'daily',
  priority: 0.7,
  sitemapSize: 5000,
  
  robotsTxtOptions: {
    additionalSitemaps: [
      `${process.env.SITE_URL || 'https://freehubgames.com'}/server-sitemap.xml`,
    ],
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/auth/', '/_next/', '/admin/'],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        crawlDelay: 1,
      },
    ],
  },
  
  exclude: [
    '/server-sitemap.xml', 
    '/api/*', 
    '/auth/*', 
    '/_next/*',
    '/admin/*',
    '/404',
    '/500'
  ],
  
  alternateRefs: [
    {
      href: `${process.env.SITE_URL || 'https://freehubgames.com'}/en`,
      hreflang: 'en',
    },
    {
      href: `${process.env.SITE_URL || 'https://freehubgames.com'}/zh`,
      hreflang: 'zh',
    },
    {
      href: `${process.env.SITE_URL || 'https://freehubgames.com'}`,
      hreflang: 'x-default',
    },
  ],
  
  // 自定义转换函数
  transform: async (config, path) => {
    // 为游戏页面设置更高的优先级
    if (path.includes('/game/')) {
      return {
        loc: path,
        changefreq: 'weekly',
        priority: 0.9,
        lastmod: new Date().toISOString(),
      }
    }
    
    // 为分类页面设置中等优先级
    if (path.includes('/games/')) {
      return {
        loc: path,
        changefreq: 'daily',
        priority: 0.8,
        lastmod: new Date().toISOString(),
      }
    }
    
    // 首页最高优先级
    if (path === '/en' || path === '/zh' || path === '/') {
      return {
        loc: path,
        changefreq: 'daily',
        priority: 1.0,
        lastmod: new Date().toISOString(),
      }
    }
    
    // 默认配置
    return {
      loc: path,
      changefreq: config.changefreq,
      priority: config.priority,
      lastmod: new Date().toISOString(),
    }
  },
} 