"use client";

import { useTranslations } from 'next-intl';
import { gamesData, getGamesByTag } from '@/data/games';
import GameCard from '@/components/GameCard';
import { motion, AnimatePresence } from 'framer-motion';
import { use, Suspense } from 'react';
import { ErrorBoundary } from '@/components/ErrorBoundary';

// 有效的游戏分类
const VALID_CATEGORIES = [
  'action',
  'adventure',
  'puzzle',
  'strategy',
  'arcade',
  'sports',
  'racing',
  'shooting',
  'horror',
  'simulation',
  'sandbox'
] as const;

type ValidCategory = typeof VALID_CATEGORIES[number];

interface CategoryPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

// 错误回退组件
function ErrorFallback({ error, reset }: { error: Error; reset: () => void }) {
  const t = useTranslations('Error');
  return (
    <div className="text-center py-12">
      <p className="text-lg text-red-500 mb-4">{t('somethingWentWrong')}</p>
      <pre className="text-sm text-muted-foreground mb-4">{error.message}</pre>
      <button
        onClick={reset}
        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
      >
        {t('tryAgain')}
      </button>
    </div>
  );
}

// 加载中组件
function LoadingFallback() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {Array.from({ length: 8 }).map((_, index) => (
        <div key={index} className="animate-pulse">
          <div className="aspect-video bg-secondary rounded-lg mb-2" />
          <div className="h-4 bg-secondary rounded w-3/4 mb-2" />
          <div className="h-4 bg-secondary rounded w-1/2" />
        </div>
      ))}
    </div>
  );
}

function CategoryContent({ id, locale }: { id: string; locale: string }) {
  const t = useTranslations('Navigation.categoryList');
  const tHome = useTranslations('Home');

  // 验证分类ID是否有效
  const isValidCategory = (categoryId: string): categoryId is ValidCategory => {
    return VALID_CATEGORIES.includes(categoryId as ValidCategory);
  };

  // 获取游戏列表，添加错误处理
  const getGamesForCategory = (categoryId: string) => {
    if (!isValidCategory(categoryId)) {
      console.warn(`Invalid category ID: ${categoryId}`);
      return [];
    }

    // 首先检查主分类
    const mainCategoryGames = gamesData[categoryId] || [];
    
    // 然后检查标签
    const taggedGames = getGamesByTag(categoryId);
    
    // 合并并去重
    const allGames = [...mainCategoryGames, ...taggedGames];
    const uniqueGames = allGames.filter((game, index, self) =>
      index === self.findIndex((g) => g.id === game.id)
    );

    return uniqueGames;
  };

  const games = getGamesForCategory(id);

  // 动画变量
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div 
        key={id}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            {isValidCategory(id) ? t(id) : id}
          </h1>
          <p className="text-lg text-muted-foreground max-w-3xl">
            {tHome('exploreGames')} - {isValidCategory(id) ? t(id) : id}. {tHome('discoverMore')}
          </p>
        </div>

        <div className="mb-6">
          <h2 className="text-2xl font-bold mb-4">
            {tHome('gameCollection')} ({games.length} {tHome('games')})
          </h2>
        </div>

        <motion.div
          variants={container}
          initial="hidden"
          animate="show"
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
        >
          {games.length > 0 ? (
            games.map((game) => (
              <motion.div key={`${id}-${game.id}`} variants={item} layout>
                <GameCard game={game} locale={locale} priority={games.indexOf(game) < 4} />
              </motion.div>
            ))
          ) : (
            <motion.div 
              className="col-span-full text-center py-12"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <p className="text-lg text-muted-foreground">
                {t('noCategoryGames')}
              </p>
            </motion.div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const resolvedParams = use(params);

  return (
    <div className="py-8">
      <div className="container mx-auto px-4">
        <ErrorBoundary fallback={ErrorFallback}>
          <Suspense fallback={<LoadingFallback />}>
            <CategoryContent 
              id={resolvedParams.id} 
              locale={resolvedParams.locale} 
            />
          </Suspense>
        </ErrorBoundary>
      </div>
    </div>
  );
} 