import { getLandingPage } from "@/app/actions";
import { unstable_setRequestLocale } from 'next-intl/server';
import { routing } from '@/i18n/routing';
import HomePage from './home';
import { Metadata } from 'next';
import Link from 'next/link';


type Locale = (typeof routing.locales)[number];

export const metadata: Metadata = {
  title: 'FreeHubGames - Play 4,500+ Free Online Games | No Download Required',
  description: 'Play the best free online games instantly! 4,500+ games including action, adventure, racing, puzzle games. No downloads, no ads, just pure gaming fun.',
  keywords: 'free online games, browser games, no download games, action games, puzzle games, racing games, adventure games',
  openGraph: {
    title: 'FreeHubGames - Play 4,500+ Free Online Games',
    description: 'Play the best free online games instantly! 4,500+ games, no downloads required.',
    type: 'website',
    siteName: 'FreeHubGames',
  },
  twitter: {
    title: 'FreeHubGames - Play 4,500+ Free Online Games',
    description: 'Play the best free online games instantly! 4,500+ games, no downloads required.',
    card: 'summary_large_image',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://freehubgames.com',
  }
};

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;

  // 设置请求的 locale
  unstable_setRequestLocale(locale);

  // 获取页面数据
  const page = await getLandingPage(locale);

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "FreeHubGames",
            "url": "https://freehubgames.com",
            "description": "Play the best free online games instantly! 4,500+ games including action, adventure, racing, puzzle games.",
            "potentialAction": {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": "https://freehubgames.com/search?q={search_term_string}"
              },
              "query-input": "required name=search_term_string"
            },
            "publisher": {
              "@type": "Organization",
              "name": "FreeHubGames"
            }
          })
        }}
      />
      
      {/* Main Content */}
      <main>
        <HomePage />
      </main>
      
      {/* Additional SEO-focused sections */}
      <section className="py-12 bg-secondary/30">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6 text-center">About FreeHubGames</h2>
          <div className="max-w-4xl mx-auto text-center space-y-4">
            <p className="text-muted-foreground">
              FreeHubGames is your ultimate destination for free online gaming. With over 4,500 carefully curated games, 
              we offer the best browser-based gaming experience without downloads, intrusive ads, or pop-ups.
            </p>
            <p className="text-muted-foreground">
              Join our community of 30+ million monthly players and discover games across every genre - from action-packed 
              adventures to mind-bending puzzles. All games are instantly playable on desktop, mobile, and tablet devices.
            </p>
          </div>
        </div>
      </section>

      {/* Popular Categories for SEO */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-8 text-center">Popular Game Categories</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              { name: 'Action Games', href: `/${locale}/games/action`, description: 'Fast-paced action and combat games' },
              { name: 'Puzzle Games', href: `/${locale}/games/puzzle`, description: 'Brain-teasing puzzles and logic games' },
              { name: 'Racing Games', href: `/${locale}/games/racing`, description: 'High-speed racing and driving games' },
              { name: 'Adventure Games', href: `/${locale}/games/adventure`, description: 'Epic quests and exploration games' },
              { name: 'Shooting Games', href: `/${locale}/games/shooting`, description: 'Target practice and combat shooting' },
              { name: 'Strategy Games', href: `/${locale}/games/strategy`, description: 'Tactical planning and strategy games' },
              { name: 'Sports Games', href: `/${locale}/games/sports`, description: 'Virtual sports and athletic competitions' },
              { name: 'Simulation Games', href: `/${locale}/games/simulation`, description: 'Life simulation and management games' }
            ].map((category) => (
              <Link key={category.name} href={category.href} className="group">
                <div className="p-4 rounded-lg border border-border hover:border-primary/50 transition-colors">
                  <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                    {category.name}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {category.description}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>
    </>
  );
}
