'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

// Google Analytics 配置
const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID;

// 页面浏览事件
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
    });
  }
};

// 自定义事件
export const event = ({ action, category, label, value }: {
  action: string;
  category: string;
  label?: string;
  value?: number;
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// 游戏相关事件
export const trackGameEvent = (action: 'play' | 'fullscreen' | 'share', gameId: string, gameTitle: string) => {
  event({
    action: `game_${action}`,
    category: 'Games',
    label: `${gameId} - ${gameTitle}`,
  });
};

// 搜索事件
export const trackSearchEvent = (query: string, resultsCount: number) => {
  event({
    action: 'search',
    category: 'Search',
    label: query,
    value: resultsCount,
  });
};

// 分类浏览事件
export const trackCategoryEvent = (category: string) => {
  event({
    action: 'category_view',
    category: 'Navigation',
    label: category,
  });
};

// Analytics 组件
export default function Analytics() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (GA_TRACKING_ID) {
      const url = pathname + searchParams.toString();
      pageview(url);
    }
  }, [pathname, searchParams]);

  return null;
}

// Web Vitals 监控
export function reportWebVitals({ id, name, label, value }: any) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', name, {
      event_category: label === 'web-vital' ? 'Web Vitals' : 'Next.js custom metric',
      value: Math.round(name === 'CLS' ? value * 1000 : value),
      event_label: id,
      non_interaction: true,
    });
  }
}

// 声明全局 gtag 类型
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}