'use client';

import { useLocale } from 'next-intl';

interface StructuredDataProps {
  type: 'website' | 'game' | 'category';
  data?: {
    title?: string;
    description?: string;
    url?: string;
    image?: string;
    category?: string;
    rating?: number;
    gameUrl?: string;
  };
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const locale = useLocale();

  const getWebsiteStructuredData = () => ({
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "FreeHubGames",
    "alternateName": locale === 'zh' ? "免费游戏中心" : "Free Online Games Hub",
    "url": "https://freehubgames.com",
    "description": locale === 'zh' 
      ? "免费在线游戏平台，提供500+浏览器游戏，无需下载即可畅玩"
      : "Free online gaming platform with 500+ browser games, play instantly without downloads",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://freehubgames.com/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "FreeHubGames",
      "url": "https://freehubgames.com"
    },
    "inLanguage": [
      {
        "@type": "Language",
        "name": "English",
        "alternateName": "en"
      },
      {
        "@type": "Language", 
        "name": "Chinese",
        "alternateName": "zh"
      }
    ]
  });

  const getGameStructuredData = () => {
    if (!data) return null;
    
    return {
      "@context": "https://schema.org",
      "@type": "VideoGame",
      "name": data.title,
      "description": data.description,
      "url": data.url,
      "image": data.image,
      "genre": data.category,
      "aggregateRating": data.rating ? {
        "@type": "AggregateRating",
        "ratingValue": data.rating,
        "ratingCount": "100",
        "bestRating": "5",
        "worstRating": "1"
      } : undefined,
      "gamePlatform": "Web Browser",
      "applicationCategory": "Game",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      },
      "publisher": {
        "@type": "Organization",
        "name": "FreeHubGames"
      }
    };
  };

  const getCategoryStructuredData = () => {
    if (!data) return null;
    
    return {
      "@context": "https://schema.org",
      "@type": "CollectionPage",
      "name": data.title,
      "description": data.description,
      "url": data.url,
      "mainEntity": {
        "@type": "ItemList",
        "name": data.title,
        "description": data.description
      },
      "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://freehubgames.com"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Games",
            "item": "https://freehubgames.com/games"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": data.title,
            "item": data.url
          }
        ]
      }
    };
  };

  const getStructuredData = () => {
    switch (type) {
      case 'website':
        return getWebsiteStructuredData();
      case 'game':
        return getGameStructuredData();
      case 'category':
        return getCategoryStructuredData();
      default:
        return null;
    }
  };

  const structuredData = getStructuredData();

  if (!structuredData) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
}
